<template>
	<page-container :isShowNav="false" bgColorPage="#E2ECEE">
		<image
			:src="backgroundImage"
			:style="backgroundImageStyle"
			mode="aspectFill"
			class="w-100% h-400 fixed top-0 z-0"
		/>
		<custom-nav
			bg-color="unset"
			title=""
			:is-back="true"
		>
		</custom-nav>
		<view class="content-wrapper fixed top-200 px-24">
			<view class="server-info">123</view>
			<view class="detail">123</view>
		</view>
	</page-container>
</template>

<script setup>
	import { ref, onMounted, onUnmounted, watch, computed } from 'vue';
	import { onReady, onUnload, onShow, onHide, onLoad } from '@dcloudio/uni-app';
	import CustomNav from '@/components/customNav/index.vue';
	import PageContainer from '@/components/PageContainer/index.vue';
	import BtButton from '@/components/BtButton/index.vue';
	import { useConfigStore } from '@/store/modules/config';
	import ECharts from '@/components/ECharts/index.vue';
	import { $t } from '@/locale/index.js';
	import bgLight from '@/static/index/bg-light.png';
	import bgDark from '@/static/index/bg-dark.png';

	const backgroundImage = ref(bgLight);
	const backgroundImageStyle = ref({
		backgroundImage: `url(${backgroundImage.value})`,
		backgroundSize: 'cover',
		backgroundPosition: 'center',
	});	
</script>